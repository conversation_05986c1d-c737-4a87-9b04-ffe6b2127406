using Microsoft.Extensions.Logging;
using PRNChat.Server.Models;
using PRNChat.Shared.DTOs;
using PRNChat.Shared.Interfaces;
using Supabase;
using Supabase.Gotrue;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Client = Supabase.Client;

namespace PRNChat.Server.Services
{
    public class SupabaseAuthService : IAuthService
    {
        private readonly Client _supabaseClient;
        private readonly ILogger<SupabaseAuthService> _logger;

        public SupabaseAuthService(Client supabaseClient, ILogger<SupabaseAuthService> logger)
        {
            _supabaseClient = supabaseClient;
            _logger = logger;
        }

        public async Task<UserDTO?> LoginAsync(string email, string password)
        {
            try
            {
                var session = await _supabaseClient.Auth.SignIn(email, password);
                if (session?.User == null) return null;

                // Lấy thông tin profile từ bảng 'profiles'
                var userProfile = await GetUserByIdAsync(session.User.Id);
                return userProfile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email: {Email}", email);
                return null;
            }
        }

        public async Task<UserDTO?> RegisterAsync(string email, string password, string displayName)
        {
            try
            {
                var session = await _supabaseClient.Auth.SignUp(email, password, new SignUpOptions
                {
                    Data = new Dictionary<string, object>
                    {
                        { "display_name", displayName },
                        { "full_name", displayName }, // Khởi tạo full_name
                        { "avatar_url", $"https://api.dicebear.com/8.x/initials/svg?seed={displayName}" } // Avatar mặc định
                    }
                });

                if (session?.User == null) return null;
                
                // Supabase đã tự động tạo profile qua trigger, chỉ cần lấy lại thông tin
                return await GetUserByIdAsync(session.User.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", email);
                return null;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                await _supabaseClient.Auth.SignOut();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout.");
            }
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            try
            {
                await _supabaseClient.Auth.ResetPasswordForEmail(email);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending password reset for email: {Email}", email);
                return false;
            }
        }
        
        public async Task<UserDTO?> GetUserByIdAsync(string userId)
        {
            try
            {
                var response = await _supabaseClient.From<User>()
                    .Where(u => u.Id == userId)
                    .Single();

                if (response == null) return null;

                return new UserDTO
                {
                    Id = response.Id,
                    Email = response.Email,
                    DisplayName = response.DisplayName,
                    FullName = response.FullName,
                    AvatarUrl = response.AvatarUrl
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
                return null;
            }
        }

        public async Task<IEnumerable<UserDTO>> SearchUsersAsync(string query)
        {
            try
            {
                var response = await _supabaseClient.From<User>()
                    .Filter("display_name", Supabase.Postgrest.Constants.Operator.ILike, $"%{query}%")
                    .Limit(10)
                    .Get();

                return response.Models?.Select(u => new UserDTO
                {
                    Id = u.Id,
                    Email = u.Email,
                    DisplayName = u.DisplayName,
                    FullName = u.FullName,
                    AvatarUrl = u.AvatarUrl
                }) ?? Enumerable.Empty<UserDTO>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching users with query: {Query}", query);
                return Enumerable.Empty<UserDTO>();
            }
        }

        public async Task<bool> UpdateUserProfileAsync(UserDTO userDto)
        {
            try
            {
                var user = new User
                {
                    Id = userDto.Id,
                    DisplayName = userDto.DisplayName,
                    FullName = userDto.FullName,
                    AvatarUrl = userDto.AvatarUrl
                };

                await _supabaseClient.From<User>().Update(user);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile for user ID: {UserId}", userDto.Id);
                return false;
            }
        }
    }
}