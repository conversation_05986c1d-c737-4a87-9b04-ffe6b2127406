using Microsoft.Extensions.Logging;
using PRNChat.Server.Models;
using PRNChat.Shared.DTOs;
using PRNChat.Shared.Interfaces;
using Supabase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PRNChat.Server.Services
{
    public class SupabaseChatService : IChatService
    {
        private readonly Client _supabaseClient;
        private readonly ILogger<SupabaseChatService> _logger;

        public SupabaseChatService(Client supabaseClient, ILogger<SupabaseChatService> logger)
        {
            _supabaseClient = supabaseClient;
            _logger = logger;
        }

        public async Task<ChatRoomDTO?> CreateChatRoomAsync(ChatRoomDTO chatRoomDto)
        {
            try
            {
                var newRoom = new ChatRoom
                {
                    Name = chatRoomDto.Name,
                    IsGroup = chatRoomDto.Type == ChatRoomType.Group,
                    CreatedAt = DateTime.UtcNow
                };

                var roomResponse = await _supabaseClient.From<ChatRoom>().Insert(newRoom);
                var createdRoom = roomResponse.Models.FirstOrDefault();

                if (createdRoom == null) throw new Exception("Failed to create room.");

                // Thêm các thành viên vào phòng
                foreach (var memberId in chatRoomDto.Members)
                {
                    await AddUserToChatRoomAsync(createdRoom.Id, memberId);
                }

                chatRoomDto.Id = createdRoom.Id;
                return chatRoomDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating chat room.");
                return null;
            }
        }

        public async Task<IEnumerable<ChatRoomDTO>> GetChatRoomsAsync(string userId)
        {
            try
            {
                var participantResponse = await _supabaseClient.From<RoomParticipant>()
                    .Select("room_id")
                    .Where(p => p.UserId == userId)
                    .Get();

                if (participantResponse.Models == null || !participantResponse.Models.Any())
                {
                    return Enumerable.Empty<ChatRoomDTO>();
                }

                var roomIds = participantResponse.Models.Select(p => p.RoomId).ToList();

                var roomsResponse = await _supabaseClient.From<ChatRoom>()
                    .Filter("id", Supabase.Postgrest.Constants.Operator.In, roomIds)
                    .Get();
                
                return roomsResponse.Models?.Select(r => new ChatRoomDTO
                {
                    Id = r.Id,
                    Name = r.Name,
                    Type = r.IsGroup ? ChatRoomType.Group : ChatRoomType.Private,
                    CreatedAt = r.CreatedAt
                }) ?? Enumerable.Empty<ChatRoomDTO>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat rooms for user {UserId}", userId);
                return Enumerable.Empty<ChatRoomDTO>();
            }
        }

        public async Task<IEnumerable<MessageDTO>> GetMessagesAsync(string chatRoomId, int limit = 50, int offset = 0)
        {
            try
            {
                var response = await _supabaseClient.From<Message>()
                    .Select("*")
                    .Where(m => m.RoomId == chatRoomId)
                    .Order(m => m.SentAt, Supabase.Postgrest.Constants.Ordering.Descending)
                    .Range(offset, offset + limit - 1)
                    .Get();

                return response.Models?.Select(m => new MessageDTO
                {
                    Id = m.Id,
                    ChatRoomId = m.RoomId,
                    SenderId = m.SenderId,
                    Content = m.Content,
                    Timestamp = m.SentAt,
                    Type = Enum.TryParse<MessageType>(m.MessageType, true, out var type) ? type : MessageType.Text
                }) ?? Enumerable.Empty<MessageDTO>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for chat room {ChatRoomId}", chatRoomId);
                return Enumerable.Empty<MessageDTO>();
            }
        }

        public async Task<MessageDTO?> SendMessageAsync(MessageDTO messageDto)
        {
            try
            {
                var message = new Message
                {
                    RoomId = messageDto.ChatRoomId,
                    SenderId = messageDto.SenderId,
                    Content = messageDto.Content,
                    SentAt = DateTime.UtcNow,
                    MessageType = messageDto.Type.ToString().ToLower(),
                    IsBot = false
                };

                var response = await _supabaseClient.From<Message>().Insert(message);
                var createdMessage = response.Models.FirstOrDefault();

                if (createdMessage != null)
                {
                    messageDto.Id = createdMessage.Id;
                    messageDto.Timestamp = createdMessage.SentAt;
                    return messageDto;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message");
                return null;
            }
        }

        public async Task<bool> AddUserToChatRoomAsync(string chatRoomId, string userId)
        {
            try
            {
                var participant = new RoomParticipant
                {
                    RoomId = chatRoomId,
                    UserId = userId,
                    JoinedAt = DateTime.UtcNow
                };
                await _supabaseClient.From<RoomParticipant>().Insert(participant);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding user {UserId} to room {ChatRoomId}", userId, chatRoomId);
                return false;
            }
        }

        // Các phương thức khác như Update, Delete bạn có thể bổ sung tương tự
        public Task<ChatRoomDTO> GetChatRoomAsync(string chatRoomId) => throw new NotImplementedException();
        public Task<ChatRoomDTO> UpdateChatRoomAsync(ChatRoomDTO chatRoomDto) => throw new NotImplementedException();
        public Task<bool> DeleteChatRoomAsync(string chatRoomId) => throw new NotImplementedException();
        public Task<bool> RemoveUserFromChatRoomAsync(string chatRoomId, string userId) => throw new NotImplementedException();
    }
}