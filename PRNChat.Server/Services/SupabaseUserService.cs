using Microsoft.Extensions.Logging;
using PRNChat.Server.Models;
using Supabase;
using Supabase.Postgrest.Attributes;
using Supabase.Postgrest.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PRNChat.Server.Services
{
    // --- <PERSON><PERSON> sung các Model cần thiết ---
    [Table("friend_requests")]
    public class FriendRequest : BaseModel
    {
        [PrimaryKey("id")]
        public long Id { get; set; }
        [Column("requester_id")]
        public string RequesterId { get; set; }
        [Column("receiver_id")]
        public string ReceiverId { get; set; }
        [Column("status")]
        public string Status { get; set; } // pending, accepted, declined
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
    }

    [Table("friend_relations")]
    public class FriendRelation : BaseModel
    {
        [PrimaryKey("id")]
        public long Id { get; set; }
        [Column("user_id")]
        public string UserId { get; set; }
        [Column("friend_id")]
        public string FriendId { get; set; }
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
    }
    // ------------------------------------


    public class SupabaseUserService
    {
        private readonly Client _supabaseClient;
        private readonly ILogger<SupabaseUserService> _logger;

        public SupabaseUserService(Client supabaseClient, ILogger<SupabaseUserService> logger)
        {
            _supabaseClient = supabaseClient;
            _logger = logger;
        }

        public async Task<IEnumerable<User>> GetContactsAsync(string userId)
        {
            try
            {
                // Lấy ID của tất cả bạn bè
                var relations = await _supabaseClient.From<FriendRelation>()
                    .Select("friend_id")
                    .Where(r => r.UserId == userId)
                    .Get();

                if (relations.Models == null || !relations.Models.Any())
                    return Enumerable.Empty<User>();

                var friendIds = relations.Models.Select(r => r.FriendId).ToList();

                // Lấy thông tin profile của bạn bè
                var friends = await _supabaseClient.From<User>()
                    .Filter("id", Supabase.Postgrest.Constants.Operator.In, friendIds)
                    .Get();

                return friends.Models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting contacts for user {UserId}", userId);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<bool> SendFriendRequestAsync(string requesterId, string receiverId)
        {
            try
            {
                var request = new FriendRequest
                {
                    RequesterId = requesterId,
                    ReceiverId = receiverId,
                    Status = "pending",
                    CreatedAt = DateTime.UtcNow
                };
                await _supabaseClient.From<FriendRequest>().Insert(request);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending friend request from {RequesterId} to {ReceiverId}", requesterId, receiverId);
                return false;
            }
        }
        
        public async Task<bool> AcceptFriendRequestAsync(long requestId)
        {
            using var transaction = await _supabaseClient.Postgrest.BeginTransaction();
            try
            {
                // Lấy thông tin request
                var request = await transaction.From<FriendRequest>().Where(r => r.Id == requestId).Single();
                if (request == null || request.Status != "pending")
                {
                    await transaction.Rollback();
                    return false;
                }

                // Cập nhật trạng thái request
                request.Status = "accepted";
                await transaction.From<FriendRequest>().Update(request);

                // Tạo mối quan hệ bạn bè 2 chiều
                var relation1 = new FriendRelation { UserId = request.RequesterId, FriendId = request.ReceiverId, CreatedAt = DateTime.UtcNow };
                var relation2 = new FriendRelation { UserId = request.ReceiverId, FriendId = request.RequesterId, CreatedAt = DateTime.UtcNow };

                await transaction.From<FriendRelation>().Insert(new[] { relation1, relation2 });

                await transaction.Commit();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.Rollback();
                _logger.LogError(ex, "Error accepting friend request {RequestId}", requestId);
                return false;
            }
        }

        public async Task<bool> RemoveFriendAsync(string userId, string friendId)
        {
            try
            {
                // Xóa mối quan hệ 2 chiều
                await _supabaseClient.From<FriendRelation>()
                    .Where(r => r.UserId == userId && r.FriendId == friendId)
                    .Delete();
                
                await _supabaseClient.From<FriendRelation>()
                    .Where(r => r.UserId == friendId && r.FriendId == userId)
                    .Delete();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing friend {FriendId} for user {UserId}", friendId, userId);
                return false;
            }
        }
    }
}